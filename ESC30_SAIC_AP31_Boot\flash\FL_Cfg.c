/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <FL_Cfg.c>
 *  @brief      <Flash Loader Configuration >
 *               describe the block infomation.
 *  
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *
 *  <AUTHOR>
 *  @date       <2012-12-27>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121227    Gary       Initial version
 *
 *  V1.1    20130913    ccl        update
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#include "FL.h"


#define APP_INTEGRITY_ADDR 0xA0080000uL //0xA0028000
#define CAL_INTEGRITY_ADDR 0xA0028000
#define EXT_INTEGRITY_ADDR 0x05000000
/*=======[E X T E R N A L   D A T A]==========================================*/
uint8 CurrentProgrammingBlock=0xFF;
uint32 CurrentErasingAddress=0x00;
uint32 CurrentErasingLength=0x00;
const FL_BlockDescriptorType FL_BlkInfo[FL_NUM_LOGICAL_BLOCKS ] =
    {
        { 0x00000000uL, 0x216uL,EXTERNAL_FLS,HEAD_ADDR,APP_INTEGRITY_ADDR,TRUE, 0xFFFFu,0x01 },    //code
//        { 0xA0028000uL, 0x1D8000uL,INTERNAL_FLS,HEAD_ADDR,APP_INTEGRITY_ADDR,TRUE, 0xFFFFu,0x01 },    //code
        { 0xA0080000uL, 0x180000uL,INTERNAL_FLS,HEAD_ADDR,APP_INTEGRITY_ADDR,TRUE, 0xFFFFu,0x01 },
    };
uint8 FL_Header_Buffer[640u];

/*=======[E N D   O F   F I L E]==============================================*/

