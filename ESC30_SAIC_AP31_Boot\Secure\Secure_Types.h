/*******************************************************************************
 * Copyright (C) 2023 Technomous. All rights reserved         *
 ******************************************************************************/
/**
 *  \file
 *      Secure_Types.h
 *  \brief
 *      Config the secure boot type definition. 
 *	\author
 *		Ou Hengyue
 */

#ifndef SECURE_TYPES_H
#define SECURE_TYPES_H
/*****************************************************************************
*   Include Files
*****************************************************************************/
#include "Std_Types.h"
#include "Secure_Cfg.h"
/*****************************************************************************
*   Global Define Definitions
*****************************************************************************/
/* SECURE module execute result */
#define SECURE_OK               0x00u
#define SECURE_PASS             0x00u
#define SECURE_FAILED           0x01u   
#define SECURE_SAVING           0x02u
#define SECURE_SAVE_FINISH      0x03u
#define SECURE_SAVE_OVERFLOW    0x04u

#define SECURE_MODULEID_UNMATCH 0x05u
#define SECURE_CERT_VERIF_FAIL  0x06u
#define SECURE_SIGN_VERIF_FAIL  0x07u
#define SECURE_VERSION_TOOLOW   0x08u
#define SECURE_OUT_OF_RANGE     0x09u

#define SECURE_ERRORSEQUENCE    0x10u

/*****************************************************************************
*   Global Type Definitions
*****************************************************************************/
typedef struct
{
    uint8 chkResult;
    uint8 digest[32];
} Secure_IntlSignDigestType;
typedef struct
{
    uint8 chkResult;
    uint8 digest[32];
} Secure_NtlSignDigestType;
typedef union 
{
    uint8 datas[164];
    struct {
        uint8 certFmt;
        uint8 pModNum[8];
        uint8 customPars[16];
        uint8 certFailDate[3];
        uint8 certSequenceNum[4];
        uint8 signAlgoFlg;
        uint8 pubKeyCurPar;
        uint8 hashAlgoFlg;
        uint8 pubKeyIdx;
        uint8 certPubKey[64];
        uint8 certSigner[64];
    } parameters;
} Secure_SignerInfoType;

typedef union 
{
    uint8 datas[SECURE_BYPASSLIC_LEN];
    struct {
        uint8 moduleId[2];
        Secure_SignerInfoType signerInfoNtl; // National
        uint8 signNtl[64];
        Secure_SignerInfoType signerInfoIntl; // International
        uint8 signIntl[64];
    } parameters;
} Secure_BypassLicenseType;

typedef union 
{
    uint8 datas[SECURE_HEADER_LEN];
    struct {
        uint8 moduleId[2];
        uint8 NBID[2];
        uint8 locInfo[SECURE_APP_LOCINFO_BYTES];
        // Secure_SignerInfoType signerInfoNtl; // National
        uint8 signerInfoNtl[164];
        uint8 msgDigestNtl[32];
        uint8 signNtl[64];
        // Secure_SignerInfoType signerInfoIntl; // International
        uint8 signerInfoIntl[164];
        uint8 msgDigestIntl[32];
        uint8 signIntl[64];
    } app_parameters;
    // struct {
    //     uint8 moduleId[2];
    //     uint8 locInfo[SECURE_APP_LOCINFO_BYTES];
    //     // Secure_SignerInfoType signerInfoNtl; // National
    //     uint8 signerInfoNtl[164];
    //     uint8 msgDigestNtl[32];
    //     uint8 signNtl[64];
    //     // Secure_SignerInfoType signerInfoIntl; // International
    //     uint8 signerInfoIntl[164];
    //     uint8 msgDigestIntl[32];
    //     uint8 signIntl[64];
    // } cal_parameters;
} Secure_SignHeaderType;

/*****************************************************************************
*   Global Macros Definitions
*****************************************************************************/

/*****************************************************************************
*   Global Data Declarations
*****************************************************************************/

/*****************************************************************************
*   Global Function Declarations
*****************************************************************************/

#endif/* endif SECURE_TYPES_H */
